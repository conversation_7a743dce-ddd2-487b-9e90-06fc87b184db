// src/services/commandExecutor.ts
import type { ParsedCommand } from './chatbotService';
import type { ApiAnalysisResponse, AIAgentType } from '../types';
import type { AppNodeType } from '../App';
import { projectDataManager } from '../utils/projectDataManager';

export interface ExecutionContext {
  onNodeUpdate: (nodeId: string, updates: any) => void;
  onAddAIAgent: (agentType: AIAgentType) => void;
  onShowToast: (message: string, type: 'info' | 'error' | 'success' | 'warning') => void;
  currentAnalysisData: ApiAnalysisResponse | null;
  nodes: AppNodeType[];
  onRefreshProject?: () => void; // Optional callback to refresh project data
}

export interface ExecutionResult {
  success: boolean;
  response: string;
  actions: string[];
  suggestions?: string[];
  error?: string;
}

export class CommandExecutor {
  async executeCommand(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    try {
      console.log('Executing command:', command);
      
      switch (command.action) {
        case 'add_agent_to_scene':
          return await this.addAgentToScene(command, context);
        
        case 'create_new_scene':
          return await this.createNewScene(command, context);
        
        case 'delete_scene':
          return await this.deleteScene(command, context);
        
        case 'enhance_video_quality':
          return await this.enhanceVideoQuality(command, context);
        
        case 'analyze_scenes':
          return await this.analyzeScenes(command, context);
        
        case 'display_project_stats':
          return await this.displayProjectStats(command, context);
        
        case 'show_help':
          return await this.showHelp(command, context);
        
        case 'add_subtitles_to_all':
          return await this.addSubtitlesToAll(command, context);

        case 'apply_workflow':
          return await this.applyWorkflow(command, context);

        case 'process_all_scenes':
          return await this.processAllScenes(command, context);

        case 'navigate_to_scene':
          return await this.navigateToScene(command, context);

        default:
          return this.createErrorResult(`Unknown command action: ${command.action}`, []);
      }
    } catch (error) {
      console.error('Command execution error:', error);
      return this.createErrorResult(
        `Failed to execute command: ${error instanceof Error ? error.message : 'Unknown error'}`,
        []
      );
    }
  }
  
  private async addAgentToScene(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    let { agentType, sceneIds, target } = command.parameters;

    if (!agentType) {
      return this.createErrorResult('No agent type specified', []);
    }

    // If no sceneIds provided, default to all scenes
    if (!sceneIds || sceneIds.length === 0) {
      if (context.currentAnalysisData && context.currentAnalysisData.scenes.length > 0) {
        sceneIds = context.currentAnalysisData.scenes.map(s => s.sceneId);
        target = target || 'all';
      } else {
        return this.createErrorResult('No scenes available in the current project', []);
      }
    }

    const actions: string[] = [];

    try {
      // Add AI agent for each specified scene
      for (let i = 0; i < sceneIds.length; i++) {
        context.onAddAIAgent(agentType);
        actions.push(`Added ${agentType} agent for scene ${i + 1}`);
      }

      const agentName = this.getAgentDisplayName(agentType);
      const targetDescription = target === 'all' || !target ? `all ${sceneIds.length} scenes` : `scene ${target}`;

      return {
        success: true,
        response: `✅ Successfully added ${agentName} to ${targetDescription}. The agent${sceneIds.length > 1 ? 's' : ''} have been created on the canvas. Connect them to scenes manually to start processing, or they will auto-connect if you refresh the page.`,
        actions,
        suggestions: [
          'Connect AI agents to scenes by dragging connections',
          'Check the node graph for new AI agents',
          'Processing will start automatically when connected'
        ]
      };
    } catch (error) {
      return this.createErrorResult(`Failed to add ${agentType} agent`, actions);
    }
  }
  
  private async enhanceVideoQuality(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    let { sceneIds } = command.parameters;

    // If no sceneIds provided, default to all scenes
    if (!sceneIds || sceneIds.length === 0) {
      if (context.currentAnalysisData && context.currentAnalysisData.scenes.length > 0) {
        sceneIds = context.currentAnalysisData.scenes.map(s => s.sceneId);
      } else {
        return this.createErrorResult('No scenes available for enhancement', []);
      }
    }

    const actions: string[] = [];

    try {
      // Add video enhancer agents to all specified scenes
      for (let i = 0; i < sceneIds.length; i++) {
        context.onAddAIAgent('video-enhancer');
        actions.push(`Added video enhancer to scene ${i + 1}`);
      }

      const sceneCount = sceneIds.length;

      return {
        success: true,
        response: `🎬 Started video quality enhancement for ${sceneCount} scene${sceneCount > 1 ? 's' : ''}. This includes:\n\n• Resolution upscaling\n• Noise reduction\n• Stabilization\n• Color correction\n\nProcessing will begin automatically.`,
        actions,
        suggestions: [
          'Monitor progress in the AI Agents panel',
          'Add audio enhancement for complete improvement',
          'Consider adding color grading for professional look'
        ]
      };
    } catch (error) {
      return this.createErrorResult('Failed to start video enhancement', actions);
    }
  }
  
  private async analyzeScenes(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    let { sceneIds, specific } = command.parameters;

    if (!context.currentAnalysisData) {
      return this.createErrorResult('No video project loaded', []);
    }

    // If no sceneIds provided, default to all scenes
    if (!sceneIds || sceneIds.length === 0) {
      sceneIds = context.currentAnalysisData.scenes.map(s => s.sceneId);
    }

    const scenes = context.currentAnalysisData.scenes.filter(s => sceneIds.includes(s.sceneId));
    
    if (specific && scenes.length === 1) {
      const scene = scenes[0];
      const duration = Math.round(scene.duration);
      const tags = scene.tags.length > 0 ? scene.tags.join(', ') : 'No tags';
      
      return {
        success: true,
        response: `📊 Scene ${scene.scene_index + 1} Analysis:\n\n🎬 Title: ${scene.title}\n⏱️ Duration: ${duration}s (${scene.start}s - ${scene.end}s)\n🏷️ Tags: ${tags}\n🔊 Audio Level: ${scene.avg_volume ? 'Normal' : 'Low'}\n⚡ Energy: ${scene.high_energy ? 'High' : 'Low'}\n🔄 Transition: ${scene.transition_type || 'Cut'}`,
        actions: ['Analyzed scene details'],
        suggestions: [
          'Add content analyzer for deeper insights',
          'Enhance this scene with AI agents',
          'Edit scene metadata'
        ]
      };
    } else {
      const totalDuration = scenes.reduce((sum, s) => sum + s.duration, 0);
      const avgDuration = totalDuration / scenes.length;
      const highEnergyScenes = scenes.filter(s => s.high_energy).length;
      
      return {
        success: true,
        response: `📊 Project Analysis:\n\n🎬 Total Scenes: ${scenes.length}\n⏱️ Total Duration: ${Math.round(totalDuration)}s\n📈 Average Scene Length: ${Math.round(avgDuration)}s\n⚡ High Energy Scenes: ${highEnergyScenes}\n🎵 Audio Quality: ${scenes.filter(s => s.avg_volume).length}/${scenes.length} scenes have good audio`,
        actions: ['Analyzed project statistics'],
        suggestions: [
          'Add content analyzers to all scenes',
          'Enhance scenes with low audio quality',
          'Balance scene lengths for better pacing'
        ]
      };
    }
  }
  
  private async displayProjectStats(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    if (!context.currentAnalysisData) {
      return this.createErrorResult('No video project loaded', []);
    }
    
    const { sceneCount, totalDuration } = command.parameters;
    const scenes = context.currentAnalysisData.scenes;
    
    // Calculate additional statistics
    const avgSceneLength = totalDuration / sceneCount;
    const shortScenes = scenes.filter(s => s.duration < 5).length;
    const longScenes = scenes.filter(s => s.duration > 30).length;
    const taggedScenes = scenes.filter(s => s.tags.length > 0).length;
    const aiAgentNodes = context.nodes.filter(n => n.data.type === 'ai-agent').length;
    
    return {
      success: true,
      response: `📊 Project Statistics:\n\n🎬 Video: ${context.currentAnalysisData.fileName}\n📹 Total Scenes: ${sceneCount}\n⏱️ Total Duration: ${Math.round(totalDuration)}s (${Math.round(totalDuration/60)}m)\n📏 Average Scene Length: ${Math.round(avgSceneLength)}s\n\n📊 Scene Breakdown:\n• Short scenes (<5s): ${shortScenes}\n• Long scenes (>30s): ${longScenes}\n• Tagged scenes: ${taggedScenes}/${sceneCount}\n\n🤖 AI Agents: ${aiAgentNodes} active\n\n💡 Recommendations:\n${this.generateRecommendations(scenes, aiAgentNodes)}`,
      actions: ['Generated project statistics'],
      suggestions: [
        'Optimize scene lengths for better pacing',
        'Add more AI agents for enhancement',
        'Tag scenes for better organization'
      ]
    };
  }
  
  private async showHelp(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const helpText = `🎬 AI Video Editing Assistant - Available Commands:

🤖 AI AGENTS:
• "Add subtitle agent to scene 1"
• "Add video enhancer to all scenes"
• "Add audio processor to scene 3"
• "Add content analyzer to every scene"

🎞️ SCENE OPERATIONS:
• "Create new scene after scene 2"
• "Delete scene 3"
• "Analyze scene 1"
• "Show scene statistics"

✨ ENHANCEMENTS:
• "Enhance video quality for all scenes"
• "Add subtitles to all scenes"
• "Improve audio quality"

📊 ANALYSIS:
• "Show project statistics"
• "Analyze all scenes"
• "Tell me about scene 2"

💡 TIPS:
• Use natural language - I understand context!
• Reference scenes by number (e.g., "scene 1")
• Use "all scenes" for batch operations
• Ask "what can you do?" anytime for help`;

    return {
      success: true,
      response: helpText,
      actions: ['Displayed help information'],
      suggestions: [
        'Try: "Add subtitle agent to scene 1"',
        'Try: "Enhance video quality for all scenes"',
        'Try: "Show me project statistics"'
      ]
    };
  }
  
  private async addSubtitlesToAll(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    let { sceneIds } = command.parameters;

    // If no sceneIds provided, default to all scenes
    if (!sceneIds || sceneIds.length === 0) {
      if (context.currentAnalysisData && context.currentAnalysisData.scenes.length > 0) {
        sceneIds = context.currentAnalysisData.scenes.map(s => s.sceneId);
      } else {
        return this.createErrorResult('No scenes available for subtitle generation', []);
      }
    }

    const actions: string[] = [];

    try {
      // Add subtitle generator to all scenes
      for (let i = 0; i < sceneIds.length; i++) {
        context.onAddAIAgent('subtitle-generator');
        actions.push(`Added subtitle generator to scene ${i + 1}`);
      }

      return {
        success: true,
        response: `📝 Started subtitle generation for all ${sceneIds.length} scenes. This will:\n\n• Transcribe audio to text\n• Generate accurate timing\n• Create downloadable SRT files\n• Sync with video playback\n\nProcessing will begin automatically for each scene.`,
        actions,
        suggestions: [
          'Monitor subtitle generation progress',
          'Review and edit subtitles when complete',
          'Download SRT files for external use'
        ]
      };
    } catch (error) {
      return this.createErrorResult('Failed to start subtitle generation', actions);
    }
  }
  
  private async createNewScene(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const { position } = command.parameters;

    if (!context.currentAnalysisData) {
      return this.createErrorResult('No video project loaded. Please upload and analyze a video first.', []);
    }

    try {
      // For now, we'll create a conceptual scene since we can't actually split video
      // This would be a placeholder scene that users can later edit
      const currentProject = projectDataManager.getCurrentProject();
      if (!currentProject) {
        return this.createErrorResult('No active project found', []);
      }

      const sceneCount = currentProject.scenes.length;
      const insertPosition = Math.min(Math.max(position || sceneCount + 1, 1), sceneCount + 1);

      // Create a new scene placeholder
      const newSceneId = `scene-${Date.now()}`;
      const duration = 5; // Default 5 second placeholder

      // This is a conceptual implementation - in a real system you'd need video splitting
      const newScene = {
        sceneId: newSceneId,
        analysisId: currentProject.analysisId,
        originalStart: 0,
        originalEnd: duration,
        originalDuration: duration,
        originalVideoFileName: currentProject.videoFileName,
        currentStart: 0,
        currentEnd: duration,
        currentDuration: duration,
        displayOrder: insertPosition - 1,
        title: `New Scene ${insertPosition}`,
        tags: ['user-created'],
        timelineEdits: {
          clips: [],
          playhead: 0,
          effects: [],
          volume: 1,
          brightness: 0,
          contrast: 0,
          saturation: 0,
          textOverlays: [],
          fadeIn: 0,
          fadeOut: 0,
          lastModified: Date.now()
        },
        aiResults: {},
        position: { x: (insertPosition - 1) * 220, y: 100 }
      };

      // Add to project (this is conceptual - real implementation would need video processing)
      currentProject.scenes.splice(insertPosition - 1, 0, newScene);

      // Update display orders
      currentProject.scenes.forEach((scene, index) => {
        scene.displayOrder = index;
      });

      // Save project
      await projectDataManager.saveProject(currentProject);

      // Refresh the UI if callback provided
      if (context.onRefreshProject) {
        context.onRefreshProject();
      }

      return {
        success: true,
        response: `✅ Created new placeholder scene at position ${insertPosition}. Note: This is a conceptual scene for planning purposes. To create actual video scenes, you would need to split the video at specific timestamps.`,
        actions: [`Created scene "${newScene.title}"`],
        suggestions: [
          'Edit the scene title and add tags',
          'Add AI agents to process this scene',
          'Use timeline editor to set actual video segments'
        ]
      };
    } catch (error) {
      return this.createErrorResult(`Failed to create scene: ${error instanceof Error ? error.message : 'Unknown error'}`, []);
    }
  }
  
  private async deleteScene(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const { sceneIndex } = command.parameters;

    if (!context.currentAnalysisData) {
      return this.createErrorResult('No video project loaded', []);
    }

    if (sceneIndex < 0 || sceneIndex >= context.currentAnalysisData.scenes.length) {
      return this.createErrorResult(`Invalid scene number. Please specify a scene between 1 and ${context.currentAnalysisData.scenes.length}`, []);
    }

    try {
      const currentProject = projectDataManager.getCurrentProject();
      if (!currentProject) {
        return this.createErrorResult('No active project found', []);
      }

      const sceneToDelete = currentProject.scenes[sceneIndex];
      if (!sceneToDelete) {
        return this.createErrorResult(`Scene ${sceneIndex + 1} not found`, []);
      }

      const sceneTitle = sceneToDelete.title;
      const sceneId = sceneToDelete.sceneId;

      // Use the optimized system's deleteScene method
      await projectDataManager.deleteScene(sceneId);

      // Refresh the UI if callback provided
      if (context.onRefreshProject) {
        context.onRefreshProject();
      }

      return {
        success: true,
        response: `✅ Successfully deleted scene ${sceneIndex + 1}: "${sceneTitle}". The remaining scenes have been automatically reordered.`,
        actions: [`Deleted scene "${sceneTitle}"`],
        suggestions: [
          'Check the updated scene list',
          'Verify scene numbering is correct',
          'Add new scenes if needed'
        ]
      };
    } catch (error) {
      return this.createErrorResult(`Failed to delete scene: ${error instanceof Error ? error.message : 'Unknown error'}`, []);
    }
  }
  
  private createErrorResult(message: string, actions: string[]): ExecutionResult {
    return {
      success: false,
      response: `❌ ${message}`,
      actions,
      error: message
    };
  }
  
  private getAgentDisplayName(agentType: AIAgentType): string {
    const displayNames: Record<AIAgentType, string> = {
      'subtitle-generator': 'Subtitle Generator',
      'video-enhancer': 'Video Enhancer',
      'audio-processor': 'Audio Processor',
      'content-analyzer': 'Content Analyzer',
      'color-grader': 'Color Grader',
      'object-detector': 'Object Detector',
      'scene-classifier': 'Scene Classifier',
      'transition-suggester': 'Transition Suggester',
      'noise-reducer': 'Noise Reducer',
      'auto-editor': 'Auto Editor'
    };
    
    return displayNames[agentType] || agentType;
  }
  
  private generateRecommendations(scenes: any[], aiAgentCount: number): string {
    const recommendations: string[] = [];
    
    if (aiAgentCount === 0) {
      recommendations.push('• Add AI agents to enhance your video');
    }
    
    const untaggedScenes = scenes.filter(s => s.tags.length === 0).length;
    if (untaggedScenes > 0) {
      recommendations.push(`• Tag ${untaggedScenes} scenes for better organization`);
    }
    
    const shortScenes = scenes.filter(s => s.duration < 3).length;
    if (shortScenes > 0) {
      recommendations.push(`• Consider merging ${shortScenes} very short scenes`);
    }
    
    const lowAudioScenes = scenes.filter(s => !s.avg_volume).length;
    if (lowAudioScenes > 0) {
      recommendations.push(`• Enhance audio for ${lowAudioScenes} scenes`);
    }
    
    return recommendations.length > 0 ? recommendations.join('\n') : '• Your project looks well-organized!';
  }



  private async applyWorkflow(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const { workflowType, sceneIds } = command.parameters;

    if (!sceneIds || sceneIds.length === 0) {
      return this.createErrorResult('No scenes available for workflow application', []);
    }

    const actions: string[] = [];

    try {
      switch (workflowType) {
        case 'podcast':
          // Podcast workflow: audio processing + subtitles
          for (let i = 0; i < sceneIds.length; i++) {
            context.onAddAIAgent('audio-processor');
            context.onAddAIAgent('subtitle-generator');
            context.onAddAIAgent('noise-reducer');
            actions.push(`Applied podcast workflow to scene ${i + 1}`);
          }
          return {
            success: true,
            response: `🎙️ Applied podcast workflow to ${sceneIds.length} scenes:\n\n• Audio enhancement and noise reduction\n• Automatic subtitle generation\n• Audio level normalization\n\nOptimal for podcast and interview content.`,
            actions,
            suggestions: [
              'Review audio levels in each scene',
              'Edit subtitles for accuracy',
              'Add intro/outro scenes if needed'
            ]
          };

        case 'social_media':
          // Social media workflow: quick enhancement + subtitles
          for (let i = 0; i < sceneIds.length; i++) {
            context.onAddAIAgent('video-enhancer');
            context.onAddAIAgent('subtitle-generator');
            context.onAddAIAgent('color-grader');
            actions.push(`Applied social media workflow to scene ${i + 1}`);
          }
          return {
            success: true,
            response: `📱 Applied social media workflow to ${sceneIds.length} scenes:\n\n• Video quality enhancement\n• Color grading for engagement\n• Subtitle generation for accessibility\n\nOptimized for social media platforms.`,
            actions,
            suggestions: [
              'Consider adding text overlays',
              'Optimize for vertical format if needed',
              'Add engaging thumbnails'
            ]
          };

        case 'professional':
          // Professional workflow: comprehensive enhancement
          for (let i = 0; i < sceneIds.length; i++) {
            context.onAddAIAgent('video-enhancer');
            context.onAddAIAgent('audio-processor');
            context.onAddAIAgent('color-grader');
            context.onAddAIAgent('noise-reducer');
            context.onAddAIAgent('content-analyzer');
            actions.push(`Applied professional workflow to scene ${i + 1}`);
          }
          return {
            success: true,
            response: `🎬 Applied professional workflow to ${sceneIds.length} scenes:\n\n• Comprehensive video enhancement\n• Professional audio processing\n• Advanced color grading\n• Content analysis and optimization\n\nSuitable for high-quality productions.`,
            actions,
            suggestions: [
              'Review all processing results',
              'Fine-tune color grading settings',
              'Add professional transitions'
            ]
          };

        default:
          return this.createErrorResult(`Unknown workflow type: ${workflowType}`, []);
      }
    } catch (error) {
      return this.createErrorResult(`Failed to apply workflow: ${error instanceof Error ? error.message : 'Unknown error'}`, actions);
    }
  }

  private async processAllScenes(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const { sceneIds } = command.parameters;

    if (!sceneIds || sceneIds.length === 0) {
      return this.createErrorResult('No scenes available for processing', []);
    }

    const actions: string[] = [];

    try {
      // Apply comprehensive processing to all scenes
      for (let i = 0; i < sceneIds.length; i++) {
        context.onAddAIAgent('content-analyzer');
        context.onAddAIAgent('video-enhancer');
        context.onAddAIAgent('audio-processor');
        actions.push(`Applied full processing to scene ${i + 1}`);
      }

      return {
        success: true,
        response: `⚡ Started comprehensive processing for all ${sceneIds.length} scenes:\n\n• Content analysis and tagging\n• Video quality enhancement\n• Audio processing and optimization\n• Intelligent scene classification\n\nThis will provide a complete analysis and enhancement of your entire project.`,
        actions,
        suggestions: [
          'Monitor processing progress in AI Agents panel',
          'Review results when processing completes',
          'Apply additional specialized agents as needed'
        ]
      };
    } catch (error) {
      return this.createErrorResult(`Failed to process all scenes: ${error instanceof Error ? error.message : 'Unknown error'}`, actions);
    }
  }

  private async navigateToScene(command: ParsedCommand, context: ExecutionContext): Promise<ExecutionResult> {
    const { sceneIndex, sceneId } = command.parameters;

    if (!context.currentAnalysisData) {
      return this.createErrorResult('No video project loaded', []);
    }

    if (sceneIndex < 0 || sceneIndex >= context.currentAnalysisData.scenes.length) {
      return this.createErrorResult(`Invalid scene number. Please specify a scene between 1 and ${context.currentAnalysisData.scenes.length}`, []);
    }

    const scene = context.currentAnalysisData.scenes[sceneIndex];

    // This would integrate with timeline/player controls in a real implementation
    return {
      success: true,
      response: `🎯 Navigated to Scene ${sceneIndex + 1}: "${scene.title}"\n\n⏱️ Duration: ${Math.round(scene.duration)}s (${scene.start}s - ${scene.end}s)\n🏷️ Tags: ${scene.tags.length > 0 ? scene.tags.join(', ') : 'No tags'}\n\nNote: Timeline navigation would be implemented with video player integration.`,
      actions: [`Navigated to scene ${sceneIndex + 1}`],
      suggestions: [
        'Edit this scene in timeline',
        'Add AI agents to this scene',
        'Analyze scene content'
      ]
    };
  }
}

// Singleton instance
export const commandExecutor = new CommandExecutor();
