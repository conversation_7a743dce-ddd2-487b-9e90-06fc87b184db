// src/components/AISidePanel.tsx
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Bot, User, Loader2, AlertCircle, Lightbulb, Zap } from 'lucide-react';
import type { AppNodeType } from '../App';
import type { ApiAnalysisResponse, AIAgentType } from '../types';
import { chatbotService } from '../services/chatbotService';
import { commandExecutor } from '../services/commandExecutor';

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  metadata?: {
    command?: string;
    executedActions?: string[];
    suggestions?: string[];
    error?: string;
  };
}

export interface AISidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentAnalysisData: ApiAnalysisResponse | null;
  nodes: AppNodeType[];
  onNodeUpdate: (nodeId: string, updates: any) => void;
  onAddAIAgent: (agentType: AIAgentType) => void;
  onShowToast: (message: string, type: 'info' | 'error' | 'success' | 'warning') => void;
  onRefreshProject?: () => void;
}

const AISidePanel: React.FC<AISidePanelProps> = ({
  isOpen,
  onClose,
  currentAnalysisData,
  nodes,
  onNodeUpdate,
  onAddAIAgent,
  onShowToast,
  onRefreshProject
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'system',
      content: '🎬 Welcome to your AI Video Editing Assistant! I can help you manipulate scenes, add AI agents, enhance your video, and automate editing tasks. Try commands like:\n\n• "Add subtitle agent to scene 1"\n• "Enhance video quality for all scenes"\n• "Create a new scene after scene 2"\n• "Show me scene statistics"',
      timestamp: Date.now()
    }
  ]);
  
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Generate contextual suggestions based on current state
  useEffect(() => {
    const generateSuggestions = () => {
      const newSuggestions: string[] = [];
      
      if (currentAnalysisData) {
        const sceneCount = currentAnalysisData.scenes.length;
        newSuggestions.push(
          `Analyze all ${sceneCount} scenes for content`,
          'Add subtitle agents to all scenes',
          'Enhance video quality for the entire project'
        );
        
        if (sceneCount > 1) {
          newSuggestions.push('Create smooth transitions between scenes');
        }
      } else {
        newSuggestions.push(
          'Upload and analyze a new video',
          'Show me available AI agents',
          'Help me get started with video editing'
        );
      }
      
      setSuggestions(newSuggestions.slice(0, 3));
    };

    generateSuggestions();
  }, [currentAnalysisData, nodes]);

  const handleSendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isProcessing) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: content.trim(),
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);

    try {
      // Parse the command using the chatbot service
      const parsedCommand = await chatbotService.parseCommand(content, {
        currentAnalysisData,
        nodes,
        selectedScenes: []
      });

      // Execute the command
      const executionResult = await commandExecutor.executeCommand(parsedCommand, {
        onNodeUpdate,
        onAddAIAgent,
        onShowToast,
        currentAnalysisData,
        nodes,
        onRefreshProject
      });

      // Create assistant response
      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: executionResult.response,
        timestamp: Date.now(),
        metadata: {
          command: parsedCommand.intent,
          executedActions: executionResult.actions,
          suggestions: executionResult.suggestions
        }
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Show success toast if actions were executed
      if (executionResult.actions.length > 0) {
        onShowToast(`✅ Executed: ${executionResult.actions.join(', ')}`, 'success');
      }

    } catch (error) {
      console.error('Chat command error:', error);
      
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        type: 'assistant',
        content: `❌ Sorry, I couldn't process that command. ${error instanceof Error ? error.message : 'Please try rephrasing your request.'}`,
        timestamp: Date.now(),
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };

      setMessages(prev => [...prev, errorMessage]);
      onShowToast('Failed to process command', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing, currentAnalysisData, nodes, onNodeUpdate, onAddAIAgent, onShowToast]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputValue);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    inputRef.current?.focus();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-y-0 right-0 w-96 shadow-xl z-50 flex flex-col" style={{
      backgroundColor: 'var(--bg-medium)',
      borderLeft: '1px solid var(--primary-dark)',
      color: 'var(--text-light)'
    }}>
      {/* Header */}
      <div className="flex items-center justify-between p-4" style={{
        borderBottom: '1px solid var(--primary-dark)',
        background: 'linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(157, 78, 221, 0.05) 100%)'
      }}>
        <div className="flex items-center gap-2">
          <Bot className="w-5 h-5" style={{ color: 'var(--primary-light)' }} />
          <h2 className="font-semibold" style={{ color: 'var(--text-light)' }}>AI Assistant</h2>
        </div>
        <button
          onClick={onClose}
          className="p-1 rounded-md transition-colors"
          style={{
            color: 'var(--text-light)',
            opacity: 0.7
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--bg-light)';
            e.currentTarget.style.opacity = '1';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.opacity = '0.7';
          }}
          title="Close AI Assistant"
        >
          ✕
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type !== 'user' && (
              <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style={{
                backgroundColor: message.type === 'system' ? 'var(--bg-light)' : 'var(--primary-dark)'
              }}>
                {message.type === 'system' ? (
                  <Lightbulb className="w-4 h-4" style={{ color: 'var(--warning)' }} />
                ) : (
                  <Bot className="w-4 h-4" style={{ color: 'var(--text-light)' }} />
                )}
              </div>
            )}

            <div className="max-w-[80%] rounded-lg p-3" style={{
              backgroundColor: message.type === 'user'
                ? 'var(--primary)'
                : message.type === 'system'
                ? 'var(--bg-light)'
                : 'var(--bg-light)',
              color: message.type === 'user'
                ? 'white'
                : 'var(--text-light)',
              border: message.type === 'system' ? '1px solid var(--primary-dark)' : 'none'
            }}>
              <div className="whitespace-pre-wrap text-sm">{message.content}</div>
              
              {message.metadata?.suggestions && (
                <div className="mt-2 pt-2" style={{ borderTop: '1px solid var(--primary-dark)' }}>
                  <div className="text-xs mb-1" style={{ color: 'var(--text-light)', opacity: 0.7 }}>Suggestions:</div>
                  {message.metadata.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="block text-xs mb-1 hover:opacity-80 transition-opacity"
                      style={{ color: 'var(--primary-light)' }}
                    >
                      • {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {message.type === 'user' && (
              <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style={{
                backgroundColor: 'var(--primary)'
              }}>
                <User className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
        ))}
        
        {isProcessing && (
          <div className="flex gap-3 justify-start">
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{
              backgroundColor: 'var(--primary-dark)'
            }}>
              <Loader2 className="w-4 h-4 animate-spin" style={{ color: 'var(--text-light)' }} />
            </div>
            <div className="rounded-lg p-3" style={{
              backgroundColor: 'var(--bg-light)'
            }}>
              <div className="text-sm" style={{ color: 'var(--text-light)', opacity: 0.8 }}>Processing your request...</div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <div className="p-4" style={{
          borderTop: '1px solid var(--primary-dark)',
          backgroundColor: 'var(--bg-light)'
        }}>
          <div className="text-xs mb-2 flex items-center gap-1" style={{
            color: 'var(--text-light)',
            opacity: 0.8
          }}>
            <Zap className="w-3 h-3" style={{ color: 'var(--warning)' }} />
            Quick suggestions:
          </div>
          <div className="space-y-1">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="block w-full text-left text-xs p-2 rounded transition-colors"
                style={{
                  color: 'var(--primary-light)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--primary-dark)';
                  e.currentTarget.style.color = 'var(--text-light)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = 'var(--primary-light)';
                }}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4" style={{ borderTop: '1px solid var(--primary-dark)' }}>
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me to help with your video editing..."
            className="flex-1 px-3 py-2 rounded-md text-sm transition-colors"
            style={{
              backgroundColor: 'var(--bg-light)',
              color: 'var(--text-light)',
              border: '1px solid var(--primary-dark)',
              outline: 'none'
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = 'var(--primary-light)';
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = 'var(--primary-dark)';
            }}
            disabled={isProcessing}
          />
          <button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isProcessing}
            className="px-3 py-2 rounded-md transition-colors"
            style={{
              backgroundColor: inputValue.trim() && !isProcessing ? 'var(--primary)' : 'var(--bg-light)',
              color: inputValue.trim() && !isProcessing ? 'white' : 'var(--text-light)',
              opacity: inputValue.trim() && !isProcessing ? 1 : 0.5,
              cursor: inputValue.trim() && !isProcessing ? 'pointer' : 'not-allowed'
            }}
            onMouseEnter={(e) => {
              if (inputValue.trim() && !isProcessing) {
                e.currentTarget.style.backgroundColor = 'var(--primary-light)';
              }
            }}
            onMouseLeave={(e) => {
              if (inputValue.trim() && !isProcessing) {
                e.currentTarget.style.backgroundColor = 'var(--primary)';
              }
            }}
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AISidePanel;
